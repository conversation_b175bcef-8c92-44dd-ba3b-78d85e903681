import React, { useState, useEffect, useCallback } from "react";
import "./index.less";
import {
  Table,
  Space,
  Button,
  Form,
  message,
  Select,
  Input,
  InputNumber,
} from "antd";
import type { TablePaginationConfig } from "antd";
import type {
  FilterValue,
  SorterResult,
  TableCurrentDataSource,
} from "antd/es/table/interface";
import { PlusOutlined, CloseOutlined, ClearOutlined } from "@ant-design/icons";
import {
  delInternationalPrice,
  getUsersByDepartment,
  updateInternationalPrice,
  getInternationalPriceByCondition,
} from "./services";
import ActionModal from "./components/ActionModal";
import DetailModal from "./components/DetailModal";
import CabinReportEditModal from "./components/CabinReportEditModal";
import ExpandedTable from "./components/ExpandedTable";
import dayjs from "dayjs";
import { useAppSelector } from "@/store/hooks";
import useBaseData from "@/hooks/useBaseData";
import { createColumns } from "./columns";
import { InternationalPrice } from "./types";
import { useTranslation } from "react-i18next";

interface EditableCellProps extends React.HTMLAttributes<HTMLElement> {
  editing: boolean;
  dataIndex: string;
  title: any;
  inputType: "number" | "text";
  record: InternationalPrice;
  index: number;
  children: React.ReactNode;
}

const EditableCell: React.FC<EditableCellProps> = ({
  editing,
  dataIndex,
  title,
  inputType,
  record,
  index,
  children,
  ...restProps
}) => {
  const { t } = useTranslation();
  const inputNode =
    inputType === "number" ? (
      <InputNumber min={0} precision={3} style={{ width: "100%" }} />
    ) : (
      <Input />
    );

  return (
    <td {...restProps}>
      {editing ? (
        <Form.Item
          name={dataIndex}
          style={{ margin: 0 }}
          rules={[
            {
              required: true,
              message: t(
                "internationalPriceManagement.validation.pleaseInput",
                { field: title }
              ),
            },
          ]}
        >
          {inputNode}
        </Form.Item>
      ) : (
        children
      )}
    </td>
  );
};

const InternationalPriceManagement: React.FC = () => {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const [editForm] = Form.useForm();
  const [data, setData] = useState<InternationalPrice[]>([]);
  const [loading, setLoading] = useState(false);
  const [departmentUsers, setDepartmentUsers] = useState<any[]>([]);
  const [selectedUserId, setSelectedUserId] = useState<number | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [method, setMethod] = useState("");
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [cabinReportEditModalVisible, setCabinReportEditModalVisible] =
    useState(false);
  const [currentRecord, setCurrentRecord] = useState<InternationalPrice | null>(
    null
  );
  const [editingKey, setEditingKey] = useState("");
  const [expandedRowKeys, setExpandedRowKeys] = useState<React.Key[]>([]);
  const [expandedEditingKey, setExpandedEditingKey] = useState("");
  const [newlyAddedKey, setNewlyAddedKey] = useState("");
  const [pagination, setPagination] = useState({
    pageindex: 1,
    pagesize: 10,
    total: 0,
  });
  const [currentFilters, setCurrentFilters] = useState<any>({});
  const [tableFilters, setTableFilters] = useState<
    Record<string, FilterValue | null>
  >({});
  const { user } = useAppSelector((state) => state.user);
  const { currentLanguage } = useAppSelector((state) => state.language);
  const { airlineOptions, portOptions, supplierOptions, loadAll } =
    useBaseData();

  const getSupplierIdForUpdate = (
    record: { suppliername?: string; supplierid?: number | null },
    supplierName?: string
  ): number | null => {
    // 当用户角色为4时，优先使用价格信息自带的supplierid
    if (user?.useridentity === 4) {
      return record.supplierid || null;
    }

    // 其他角色用户从supplierOptions中查找匹配的供应商ID
    const selectedSupplier = supplierOptions.find(
      (supplier) =>
        supplier.value === supplierName ||
        supplier.value === record.suppliername
    );
    return selectedSupplier?.key || record.supplierid || null;
  };

  const convertFiltersToParams = (
    filters: Record<string, FilterValue | null>
  ) => {
    const params: any = {};

    // 处理priceupdatetime列的筛选
    if (filters.priceupdatetime && filters.priceupdatetime.length > 0) {
      const dateFilters = filters.priceupdatetime as string[];
      dateFilters.forEach((filter) => {
        if (filter.startsWith("leftupdatetime=")) {
          params.leftupdatetime = parseInt(
            filter.replace("leftupdatetime=", "")
          );
        } else if (filter.startsWith("rightupdatetime=")) {
          params.rightupdatetime = parseInt(
            filter.replace("rightupdatetime=", "")
          );
        }
      });
    }

    // 处理密度范围筛选
    if (filters.mindensity && filters.mindensity.length > 0) {
      const densityFilters = filters.mindensity as string[];
      densityFilters.forEach((filter) => {
        if (filter.startsWith("leftdensity:")) {
          const value = Number(filter.replace("leftdensity:", ""));
          if (!isNaN(value)) {
            params.leftdensity = value;
          }
        } else if (filter.startsWith("rightdensity:")) {
          const value = Number(filter.replace("rightdensity:", ""));
          if (!isNaN(value)) {
            params.rightdensity = value;
          }
        }
      });
    }

    // 处理航司名筛选
    if (filters.airlinename && filters.airlinename.length > 0) {
      params.airlinename = filters.airlinename.join(",");
    }

    // 处理起始港筛选
    if (filters.originport && filters.originport.length > 0) {
      params.originport = filters.originport.join(",");
    }

    // 处理目的港筛选
    if (filters.unloadingport && filters.unloadingport.length > 0) {
      params.unloadingport = filters.unloadingport.join(",");
    }

    // 处理供应商筛选
    if (filters.suppliername && filters.suppliername.length > 0) {
      params.suppliername = filters.suppliername[0];
    }

    // 处理起始班次筛选
    if (filters.originschedules && filters.originschedules.length > 0) {
      params.originschedules = filters.originschedules.join(",");
    }

    //处理编号筛选
    if (filters.ipricecode && filters.ipricecode.length > 0) {
      params.ipricecode = filters.ipricecode[0];
    }

    // 处理是否中转筛选（中转港列的筛选映射到istransfer参数）
    if (filters.transfer !== null && filters.transfer !== undefined) {
      params.istransfer = Array.isArray(filters.transfer)
        ? filters.transfer[0]
        : filters.transfer;
    }

    // 处理是否有效筛选
    if (filters.iseffective !== null && filters.iseffective !== undefined) {
      params.iseffective = Array.isArray(filters.iseffective)
        ? filters.iseffective[0]
        : filters.iseffective;
    }

    // 添加其他筛选条件
    Object.keys(filters).forEach((key) => {
      if (filters[key] && !(key in params)) {
        params[key] = filters[key];
      }
    });

    return params;
  };

  const resetFilters = () => {
    setCurrentFilters({});
    setTableFilters({});
  };

  const clearAllFilters = () => {
    setCurrentFilters({});
    setTableFilters({});

    fetchData(selectedUserId || user?.userid, {
      pageindex: 1,
      pagesize: pagination.pagesize,
    });

    setPagination((prev) => ({
      ...prev,
      pageindex: 1,
    }));

    message.success(
      t("internationalPriceManagement.messages.clearFiltersSuccess")
    );
  };

  const isEditing = (record: InternationalPrice) =>
    record.priceid.toString() === editingKey;

  const edit = (record: Partial<InternationalPrice> & { priceid: number }) => {
    editForm.setFieldsValue(record);
    setEditingKey(record.priceid.toString());
  };

  const cancel = () => setEditingKey("");

  const save = useCallback(
    async (key: string) => {
      try {
        const row = await editForm.validateFields();

        const newData = [...data];
        const index = newData.findIndex(
          (item) => key === item.priceid.toString()
        );

        if (index > -1) {
          const item = newData[index];

          // 格式化班次字符串的函数
          const formatScheduleString = (value: string): string => {
            if (!value || !value.startsWith("D")) return value;
            const digits = value.slice(1);
            if (!digits) return value;
            const sortedDigits = digits.split("").sort().join("");
            return `D${sortedDigits}`;
          };

          const supplierid = getSupplierIdForUpdate(item, row.suppliername);

          const updateData: any = {
            ...item,
            supplierid,
            mprice: row.mprice,
            nprice: row.nprice,
            q45price: row.q45price,
            q100price: row.q100price,
            q300price: row.q300price,
            q500price: row.q500price,
            q1000price: row.q1000price,
            subq45price: row.subq45price,
            subq100price: row.subq100price,
            originschedules: row.originschedules
              ? formatScheduleString(row.originschedules)
              : row.originschedules,
            transferschedules: row.transferschedules
              ? formatScheduleString(row.transferschedules)
              : row.transferschedules,
            priceid: item.priceid,
          };

          try {
            await updateInternationalPrice(updateData);
            message.success(
              t("internationalPriceManagement.messages.priceUpdateSuccess")
            );

            refreshData();
            newData.splice(index, 1, updateData);
            setData(newData);
          } catch (error) {
            message.error(
              t("internationalPriceManagement.messages.priceUpdateFailed")
            );
            console.error("更新价格失败:", error);
          }
        }
        setEditingKey("");
      } catch (errInfo) {
        message.error(
          t("internationalPriceManagement.messages.formValidationFailed")
        );
      }
    },
    [
      data,
      editForm,
      t,
      selectedUserId,
      user?.userid,
      user?.useridentity,
      pagination.pageindex,
      pagination.pagesize,
      supplierOptions,
      getSupplierIdForUpdate,
    ]
  );
  const hasEditPermission = (record?: InternationalPrice) => {
    // 只有用户角色为2或4的用户才能进行编辑/删除操作
    if (user?.useridentity !== 2 && user?.useridentity !== 4) {
      return false;
    }

    // 用户角色为2时，只能编辑自己的价格
    if (user?.useridentity === 2 && record) {
      return record.userid === user.userid;
    }

    // 用户角色为4时，可以编辑所有价格
    if (user?.useridentity === 4) {
      return true;
    }

    return false;
  };
  const handleTableChange = (
    pagination: TablePaginationConfig,
    filters: Record<string, FilterValue | null>,
    sorter:
      | SorterResult<InternationalPrice>
      | SorterResult<InternationalPrice>[],
    extra: TableCurrentDataSource<InternationalPrice>
  ) => {
    const params: any = {
      pageindex: pagination.current,
      pagesize: pagination.pageSize,
    };

    if (sorter) {
      const sortArray = Array.isArray(sorter) ? sorter : [sorter];

      params.mpricesort = null;
      params.npricesort = null;
      params.q45sort = null;
      params.subq45sort = null;
      params.subq100sort = null;

      sortArray.forEach((sort) => {
        if (sort.field && sort.order) {
          const sortValue = sort.order === "ascend" ? 1 : 2;

          switch (sort.field) {
            case "mprice":
              params.mpricesort = sortValue;
              break;
            case "nprice":
              params.npricesort = sortValue;
              break;
            case "q45price":
              params.q45sort = sortValue;
              break;
            case "subq45price":
              params.subq45sort = sortValue;
              break;
            case "subq100price":
              params.subq100sort = sortValue;
              break;
          }
        }
      });
    }

    setCurrentFilters(filters);
    setTableFilters(filters);
    const filterParams = convertFiltersToParams(filters);
    Object.assign(params, filterParams);
    fetchData(selectedUserId || user?.userid, params);
  };

  const fetchData = async (userId?: number | null, pageParams?: any) => {
    setLoading(true);

    try {
      const isInquiryPersonnel =
        user?.useridentity === 1 || user?.useridentity === 3;
      if (userId) {
        const apiParams: any = {
          userid: isInquiryPersonnel ? undefined : userId,
          pageindex: pageParams?.pageindex || pagination.pageindex || 1,
          pagesize: pageParams?.pagesize || pagination.pagesize || 10,
        };

        pageParams &&
          Object.keys(pageParams).forEach((key) => {
            if (
              !["pageIndex", "pageSize", "sortField", "sortOrder"].includes(
                key
              ) &&
              pageParams[key] !== undefined
            ) {
              apiParams[key] = pageParams[key];
            }
          });

        const res = await getInternationalPriceByCondition(apiParams);

        const { data } = res;
        if (data?.resultCode === 200) {
          const dataWithIntervalPrice = (data.data || []).map(
            (record: InternationalPrice) => ({
              ...record,
              intervalprice: record.intervalprice
                ? sortIntervalPrices([...record.intervalprice])
                : [],
            })
          );
          setData(dataWithIntervalPrice);
          setPagination({
            pageindex: apiParams?.pageindex,
            pagesize: apiParams?.pagesize,
            total: data.totalNum || 0,
          });
        } else {
          message.error(
            data.message ||
              t("internationalPriceManagement.messages.getListFailed")
          );
        }
      }
    } catch (e) {
      console.error("获取国际价格列表失败:", e);
      message.error(t("internationalPriceManagement.messages.getListFailed"));
    } finally {
      setLoading(false);
    }
  };
  const fetchDepartmentUsers = async () => {
    try {
      // 只有价格部门主管(useridentity=4)才需要获取部门用户列表
      if (user?.departmentid) {
        const res = await getUsersByDepartment({
          departmentid: user.departmentid,
        });

        if (res.data?.resultCode === 200) {
          // 过滤掉当前用户自己
          const users = res.data.data.filter(
            (u: any) => u.userid !== user.userid
          );
          setDepartmentUsers(users);
        } else {
          setDepartmentUsers([]);
        }
      }
    } catch (error) {
      console.error("获取部门用户列表失败:", error);
      message.error(
        t("internationalPriceManagement.messages.getDepartmentUsersFailed")
      );
      setDepartmentUsers([]);
    }
  };

  const handleUserChange = (userId: number | null) => {
    setSelectedUserId(userId);
    fetchData(userId || user?.userid, {
      pageindex: 1,
      pagesize: pagination.pagesize,
    });
  };

  useEffect(() => {
    fetchData(user?.userid, {
      pageindex: 1,
      pagesize: pagination.pagesize,
    });

    // 如果是价格部门主管，获取部门下的用户列表
    if (user?.useridentity === 4) {
      fetchDepartmentUsers();
    }

    loadAll();
  }, []);

  useEffect(() => {
    const handleClickOutside = (e: MouseEvent) => {
      const isTableRelated =
        (e.target as HTMLElement).closest(".ant-table") ||
        (e.target as HTMLElement).closest(".ant-input-number") ||
        (e.target as HTMLElement).closest(".ant-form-item") ||
        (e.target as HTMLElement).closest(".ant-input") ||
        (e.target as HTMLElement).closest(".save-button") ||
        (e.target as HTMLElement).closest(".cancel-button") ||
        (e.target as HTMLElement).closest(".expanded-table-container");

      if (editingKey && !isTableRelated) {
        // 点击编辑框外部时取消编辑，恢复为原来的值
        cancel();
      }

      if (expandedEditingKey && !isTableRelated) {
        // 点击编辑框外部时取消展开表格编辑，恢复为原来的值
        cancelExpandedEdit();
      }
    };

    document.addEventListener("mousedown", handleClickOutside);

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [editingKey, expandedEditingKey]);

  const formatRecordForForm = (record: InternationalPrice) => {
    return {
      ...record,
      effectivetime: record?.effectivetime
        ? dayjs(record?.effectivetime)
        : null,
      unloadingport: record?.unloadingport?.split("/"),
      originschedules:
        record?.originschedules && record.originschedules.match(/\d/g),
      transferschedules:
        record?.transferschedules && record.transferschedules.match(/\d/g),
      specialitems: record?.specialitems?.split(","),
      cabinreport:
        record?.cabinreport?.map((cabin: any) => ({
          ...cabin,
          date: cabin?.date ? dayjs(cabin.date) : undefined,
          transferdate: cabin?.transferdate
            ? dayjs(cabin.transferdate)
            : undefined,
        })) || [],
    };
  };

  const handleAdd = () => {
    setMethod("add");
    setIsModalOpen(true);
    form.resetFields();
    setSelectedUserId(null);
  };

  const handleEdit = (record: InternationalPrice) => {
    setMethod("edit");
    setIsModalOpen(true);
    form.setFieldsValue(formatRecordForForm(record));
  };

  const handleCopy = (record: InternationalPrice) => {
    setMethod("add");
    setIsModalOpen(true);
    setSelectedUserId(null);
    const { priceid, ...restRecord } = record;
    form.setFieldsValue(formatRecordForForm(restRecord as InternationalPrice));
  };

  const handleDel = async (record: InternationalPrice) => {
    try {
      const res = await delInternationalPrice({
        priceid: record?.priceid,
      });
      const { data } = res;
      if (data.resultCode === 200) {
        refreshData();
        message.success(
          t("internationalPriceManagement.messages.priceDeleteSuccess")
        );
      } else {
        message.error(
          data.message ||
            t("internationalPriceManagement.messages.deleteFailed")
        );
      }
    } catch (e) {
      console.error("删除失败:", e);
      message.error(t("internationalPriceManagement.messages.deleteFailed"));
    }
  };

  const handleCancel = () => {};

  const handleViewDetail = (record: InternationalPrice) => {
    setCurrentRecord(record);
    setDetailModalVisible(true);
  };

  const handleCloseDetail = () => {
    setDetailModalVisible(false);
    setCurrentRecord(null);
  };

  const handleEditCabinReport = (record: InternationalPrice) => {
    setCurrentRecord(record);
    setCabinReportEditModalVisible(true);
  };

  const handleCloseCabinReportEdit = () => {
    setCabinReportEditModalVisible(false);
    setCurrentRecord(null);
  };

  const handleCabinReportEditSuccess = () => {
    refreshData();
    handleCloseCabinReportEdit();
  };

  const handlePriceIncrease = (record: InternationalPrice) =>
    handleQuickPriceAdjust(record, 1);

  const handlePriceDecrease = (record: InternationalPrice) =>
    handleQuickPriceAdjust(record, -1);

  const refreshData = () => {
    const filterParams = convertFiltersToParams(currentFilters);
    fetchData(selectedUserId || user?.userid, {
      pageindex: pagination.pageindex,
      pagesize: pagination.pagesize,
      ...filterParams,
    });
  };

  const handleQuickPriceAdjust = async (
    record: InternationalPrice,
    adjustValue: number
  ) => {
    try {
      const supplierid = getSupplierIdForUpdate(record);

      const priceFields = ["q100price", "q300price", "q500price", "q1000price"];
      const updatedPrices = priceFields.reduce((acc, field) => {
        const currentValue = record[field] || 0;
        const newValue = Math.max(0, currentValue + adjustValue);
        acc[field] = Number(newValue.toFixed(2));
        return acc;
      }, {} as any);

      await updateInternationalPrice({
        ...record,
        ...updatedPrices,
        supplierid,
      });
      refreshData();
    } catch (error) {
      console.error("快速调整价格失败:", error);
      message.error(
        t("internationalPriceManagement.messages.quickAdjustFailed")
      );
    }
  };

  const sortIntervalPrices = (intervals: any[]) => {
    return intervals.sort((a, b) => {
      if (a.densitylvalue !== b.densitylvalue) {
        return a.densitylvalue - b.densitylvalue;
      }
      return a.densityrvalue - b.densityrvalue;
    });
  };

  const updateDataWithSort = (
    parentId: number,
    updateFn: (data: any[], parentIndex: number) => void
  ) => {
    const newData = [...data];
    const parentIndex = newData.findIndex((item) => item.priceid === parentId);

    if (parentIndex > -1) {
      updateFn(newData, parentIndex);

      // 如果有密度价格区间数据，进行排序
      if (newData[parentIndex].intervalprice) {
        sortIntervalPrices(newData[parentIndex].intervalprice!);
      }

      setData(newData);
    }

    return { newData, parentIndex };
  };

  const isExpandedEditing = (intervalIndex: number, parentId: number) =>
    expandedEditingKey === `${parentId}-${intervalIndex}`;

  const editExpandedRow = (
    intervalIndex: number,
    parentId: number,
    intervalData: any
  ) => {
    editForm.setFieldsValue(intervalData);
    setExpandedEditingKey(`${parentId}-${intervalIndex}`);
    setNewlyAddedKey("");
  };

  const cancelExpandedEdit = () => {
    if (newlyAddedKey && expandedEditingKey === newlyAddedKey) {
      const [parentIdStr, intervalIndexStr] = expandedEditingKey.split("-");
      const parentId = parseInt(parentIdStr);
      const intervalIndex = parseInt(intervalIndexStr);

      updateDataWithSort(parentId, (newData, parentIndex) => {
        if (newData[parentIndex].intervalprice) {
          newData[parentIndex].intervalprice!.splice(intervalIndex, 1);
        }
      });
    }

    setExpandedEditingKey("");
    setNewlyAddedKey("");
  };

  const saveExpandedRow = async (intervalIndex: number, parentId: number) => {
    try {
      const row = await editForm.validateFields();
      const newData = [...data];
      const parentIndex = newData.findIndex(
        (item) => item.priceid === parentId
      );

      if (parentIndex > -1 && newData[parentIndex].intervalprice) {
        newData[parentIndex].intervalprice![intervalIndex] = {
          ...newData[parentIndex].intervalprice![intervalIndex],
          ...row,
        };
        sortIntervalPrices(newData[parentIndex].intervalprice!);

        const supplierid = getSupplierIdForUpdate(newData[parentIndex]);
        const updatedRecord = {
          ...newData[parentIndex],
          supplierid,
          intervalprice: newData[parentIndex].intervalprice,
        };

        const res = await updateInternationalPrice(updatedRecord);
        const { data: responseData } = res;

        if (responseData.resultCode === 200) {
          setData(newData);
          message.success(
            t("internationalPriceManagement.messages.priceUpdateSuccess")
          );
        } else {
          message.error(
            responseData.message ||
              t("internationalPriceManagement.messages.updateFailed")
          );
          return;
        }
      }

      setExpandedEditingKey("");
      setNewlyAddedKey("");
    } catch (errInfo) {
      console.error("保存密度价格区间失败:", errInfo);
      message.error(
        t("internationalPriceManagement.messages.formValidationFailed")
      );
    }
  };

  const addExpandedRow = (parentId: number) => {
    try {
      const newData = [...data];
      const parentIndex = newData.findIndex(
        (item) => item.priceid === parentId
      );

      if (parentIndex > -1) {
        if (!newData[parentIndex].intervalprice) {
          newData[parentIndex].intervalprice = [];
        }

        const newInterval = {
          densitylvalue: 0,
          densityrvalue: 100,
          q100: 0,
          q300: 0,
          q500: 0,
          q1000: 0,
        };

        newData[parentIndex].intervalprice!.push(newInterval);
        setData(newData);

        const newIntervalIndex = newData[parentIndex].intervalprice!.length - 1;
        const newKey = `${parentId}-${newIntervalIndex}`;
        editForm.setFieldsValue(newInterval);
        setExpandedEditingKey(newKey);
        setNewlyAddedKey(newKey);
      }
    } catch (error) {
      console.error("新增密度价格区间失败:", error);
      message.error(t("internationalPriceManagement.messages.addFailed"));
    }
  };

  const deleteExpandedRow = async (intervalIndex: number, parentId: number) => {
    try {
      const newData = [...data];
      const parentIndex = newData.findIndex(
        (item) => item.priceid === parentId
      );

      if (parentIndex > -1 && newData[parentIndex].intervalprice) {
        newData[parentIndex].intervalprice!.splice(intervalIndex, 1);
        sortIntervalPrices(newData[parentIndex].intervalprice!);

        const updatedRecord = {
          ...newData[parentIndex],
          intervalprice: newData[parentIndex].intervalprice,
        };

        const res = await updateInternationalPrice(updatedRecord);
        const { data: responseData } = res;

        if (responseData.resultCode === 200) {
          setData(newData);
          message.success(
            t("internationalPriceManagement.messages.intervalDeleteSuccess")
          );
        } else {
          message.error(
            responseData.message ||
              t("internationalPriceManagement.messages.deleteFailed")
          );
        }
      }
    } catch (error) {
      console.error("删除密度价格区间失败:", error);
      message.error(t("internationalPriceManagement.messages.deleteFailed"));
    }
  };

  const adjustIntervalPrice = async (
    intervalIndex: number,
    parentId: number,
    adjustValue: number
  ) => {
    try {
      const newData = [...data];
      const parentIndex = newData.findIndex(
        (item) => item.priceid === parentId
      );

      if (parentIndex > -1 && newData[parentIndex].intervalprice) {
        const interval = newData[parentIndex].intervalprice![intervalIndex];
        const priceFields = ["q100", "q300", "q500", "q1000"];

        priceFields.forEach((field) => {
          const currentValue = (interval as any)[field] || 0;
          const newValue = Math.max(0, currentValue + adjustValue);
          (interval as any)[field] = Number(newValue.toFixed(3));
        });

        sortIntervalPrices(newData[parentIndex].intervalprice!);

        const supplierid = getSupplierIdForUpdate(newData[parentIndex]);
        const updatedRecord = {
          ...newData[parentIndex],
          supplierid,
          intervalprice: newData[parentIndex].intervalprice,
        };

        const res = await updateInternationalPrice(updatedRecord);
        const { data: responseData } = res;

        if (responseData.resultCode === 200) {
          setData(newData);
          message.success(`价格${adjustValue > 0 ? "增加" : "减少"}成功`);
        } else {
          message.error(
            responseData.message ||
              t("internationalPriceManagement.messages.updateFailed")
          );
        }
      }
    } catch (error) {
      console.error("调整密度价格区间失败:", error);
      message.error(t("internationalPriceManagement.messages.updateFailed"));
    }
  };
  const filterRender = () => {
    return (
      <div className="filter_box">
        <div className="filter_title">
          <div className="title">{t("internationalPriceManagement.title")}</div>
          {/* 一键清除所有筛选状态按钮 */}
          <Button
            icon={<ClearOutlined />}
            onClick={clearAllFilters}
            title={t("internationalPriceManagement.clearAllFilters")}
          >
            {t("internationalPriceManagement.clearAllFilters")}
          </Button>
        </div>
        <Space>
          {user?.useridentity === 4 && departmentUsers.length > 0 && (
            <Select
              placeholder={t(
                "internationalPriceManagement.selectDepartmentMember"
              )}
              style={{ width: 200 }}
              allowClear
              value={selectedUserId}
              onChange={handleUserChange}
              options={[
                ...departmentUsers.map((user: any) => ({
                  label:
                    user.email ||
                    user.fullname ||
                    `${t("internationalPriceManagement.user")}${user.userid}`,
                  value: user.userid,
                })),
              ]}
            />
          )}

          {(user?.useridentity === 2 || user?.useridentity === 4) && (
            <Button type="primary" icon={<PlusOutlined />} onClick={handleAdd}>
              {t("internationalPriceManagement.newPrice")}
            </Button>
          )}
        </Space>
      </div>
    );
  };

  const expandedRowRender = (record: InternationalPrice) => {
    return (
      <ExpandedTable
        record={record}
        isExpandedEditing={isExpandedEditing}
        editExpandedRow={editExpandedRow}
        cancelExpandedEdit={cancelExpandedEdit}
        saveExpandedRow={saveExpandedRow}
        addExpandedRow={addExpandedRow}
        deleteExpandedRow={deleteExpandedRow}
        hasEditPermission={hasEditPermission}
        adjustIntervalPrice={adjustIntervalPrice}
      />
    );
  };

  const columns = createColumns({
    editHandlers: {
      isEditing,
      hasEditPermission,
      edit,
      save,
      cancel,
    },
    actionHandlers: {
      handleViewDetail,
      handleCopy,
      handleEdit,
      handleDel,
      handleCancel,
      handlePriceIncrease,
      handlePriceDecrease,
      onEditCabinReport: handleEditCabinReport,
    },
    options: {
      airlineOptions,
      portOptions,
      supplierOptions,
    },
    config: {
      t,
      currentLanguage,
      tableFilters,
      batchMode: false,
      userIdentity: user?.useridentity,
    },
  });

  return (
    <>
      {!isModalOpen ? (
        <div className="international-price-management">
          {filterRender()}
          <Form form={editForm} component={false}>
            <Table<InternationalPrice>
              className="scroll-table"
              components={{
                body: {
                  cell: EditableCell,
                },
              }}
              columns={columns}
              dataSource={data}
              loading={loading}
              rowKey="priceid"
              size="middle"
              sortDirections={["ascend", "descend"]}
              pagination={{
                current: pagination.pageindex,
                pageSize: pagination.pagesize,
                total: pagination.total,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total) => t("common.total", { count: total }),
              }}
              rowClassName={(record) => {
                return record.iseffective ? "" : "invalid-row";
              }}
              onChange={handleTableChange}
              scroll={{ x: 1300, y: "65vh" }}
              expandable={{
                expandedRowRender,
                expandedRowKeys,
                onExpandedRowsChange: (keys) => setExpandedRowKeys([...keys]),
                expandRowByClick: false,
                expandIcon: ({ expanded, onExpand, record }) => (
                  <Button
                    type="text"
                    size="small"
                    icon={expanded ? <CloseOutlined /> : <PlusOutlined />}
                    onClick={(e) => onExpand(record, e)}
                    style={{
                      color: expanded ? "#ff4d4f" : "#1890ff",
                      border: "none",
                      boxShadow: "none",
                    }}
                  />
                ),
              }}
              onRow={(record) => {
                return {
                  onClick: () => {
                    if (
                      editingKey &&
                      editingKey !== record.priceid.toString()
                    ) {
                      cancel();
                    }

                    if (expandedEditingKey) {
                      cancelExpandedEdit();
                    }
                  },
                };
              }}
            />
          </Form>
        </div>
      ) : (
        <ActionModal
          open={isModalOpen}
          setOpen={setIsModalOpen}
          method={method}
          form={form}
          fetchData={fetchData}
          selectedUserId={selectedUserId}
          currentPagination={pagination}
          currentFilters={convertFiltersToParams(currentFilters)}
          onResetFilters={resetFilters}
        />
      )}

      <DetailModal
        open={detailModalVisible}
        onClose={handleCloseDetail}
        record={currentRecord}
      />

      <CabinReportEditModal
        visible={cabinReportEditModalVisible}
        onCancel={handleCloseCabinReportEdit}
        record={currentRecord}
        onSuccess={handleCabinReportEditSuccess}
      />
    </>
  );
};

export default InternationalPriceManagement;
