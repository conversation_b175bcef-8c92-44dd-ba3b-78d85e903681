.international-price-management {
  min-height: 100%;
  background-color: #fff;
  padding: 16px;
  border-radius: 8px;

  .filter_box {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    padding-bottom: 10px;
    border-bottom: 1px solid #f0f0f0;

    .filter_title {
      display: flex;
      align-items: center;
      .title {
        font-size: 16px;
        font-weight: 600;
        color: #1f2937;
        position: relative;
        padding-left: 12px;
        margin-right: 20px;

        &:before {
          content: "";
          position: absolute;
          left: 0;
          top: 50%;
          transform: translateY(-50%);
          width: 4px;
          height: 18px;
          background-color: #1797e1;
          border-radius: 2px;
        }
      }
    }
  }

  .operation-buttons {
    .ant-btn {
      border-radius: 6px;

      &.view-button {
        color: #10b981;

        &:hover {
          background-color: #ecfdf5;
        }
      }

      &.copy-button {
        color: #6366f1;

        &:hover {
          background-color: #eef2ff;
        }
      }

      &.edit-button {
        color: @primary-color;

        &:hover {
          background-color: #e6f7ff;
        }
      }

      &.delete-button {
        color: #ef4444;

        &:hover {
          background-color: #fef2f2;
        }
      }

      &.save-button {
        color: #22c55e;

        &:hover {
          background-color: #dcfce7;
        }
      }

      &.cancel-button {
        color: #f97316;

        &:hover {
          background-color: #fff7ed;
        }
      }
    }
  }

  .editable-cell-value-wrap {
    cursor: pointer;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;

    &:hover {
      border: 1px solid #d9d9d9;
      border-radius: 4px;
      padding: 0px 11px;
      background-color: #f5f5f5;
    }

    &.batch-mode-disabled {
      cursor: default;
      opacity: 0.7;

      &:hover {
        border: none;
        padding: 0;
        background-color: transparent;
      }
    }
  }

  .scroll-table {
    &.empty-table {
      .ant-table-container {
        overflow-x: hidden !important;

        .ant-table-content {
          overflow-x: hidden !important;
        }

        .ant-table-body {
          overflow-x: hidden !important;
        }
      }
    }

    .ant-table {
      min-width: 50vh;
      .ant-table-body,
      .ant-table-content {
        scrollbar-width: thin;
        scrollbar-color: #eaeaea transparent;
        scrollbar-gutter: stable;

        overflow-y: auto !important;

        &::-webkit-scrollbar {
          width: 8px;
          height: 8px;
        }

        &::-webkit-scrollbar-track {
          background: transparent;
        }

        &::-webkit-scrollbar-thumb {
          background-color: #eaeaea;
          border-radius: 4px;
          border: 2px solid transparent;
        }

        &::-webkit-scrollbar-thumb:hover {
          background-color: #d0d0d0;
        }
      }
    }
  }

  .ant-table-wrapper {
    .ant-table {
      border-radius: 8px;
      overflow: hidden;
    }

    .ant-table-thead > tr > th {
      background-color: #f5f7fa;
      color: #1f2937;
      font-weight: 500;
      border-bottom: 1px solid #e6e8eb;
      white-space: nowrap;
      overflow: visible;
      padding: 12px 8px;

      &.ant-table-cell {
        text-align: center;
      }
    }

    .ant-table-tbody > tr > td {
      padding: 10px 8px;
      text-align: center;

      &.ant-table-cell {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        .ant-form-item-control-input {
          min-height: 20px;
          height: 20px;
        }
      }
    }

    .ant-table-body {
      &::-webkit-scrollbar {
        width: 8px;
        height: 8px;
      }

      &::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 4px;
      }

      &::-webkit-scrollbar-thumb {
        background: #ccc;
        border-radius: 4px;
      }

      &::-webkit-scrollbar-thumb:hover {
        background: #999;
      }

      tr.invalid-row > td {
        background-color: #ffbfbf;
      }

      tr.invalid-row:hover > td {
        background-color: #f19191 !important;
      }
    }
  }

  .price-cell-with-horizontal-controls {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 6px;

    .price-value-area {
      flex: 1;
      text-align: center;
      cursor: pointer;
      border-radius: 4px;
      transition: all 0.2s ease;
      border: 1px solid transparent;
      font-size: 14px;

      &:hover {
        background-color: rgba(24, 144, 255, 0.08);
        border-color: rgba(24, 144, 255, 0.2);
      }
    }

    .horizontal-controls-group {
      display: flex;
      align-items: center;
      gap: 3px;
      padding: 2px 4px;
      background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
      border: 1px solid #e2e8f0;
      border-radius: 16px;
      box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
      transition: all 0.2s ease;

      &:hover {
        background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }

      .controls-label {
        font-size: 9px;
        color: #64748b;
        font-weight: 600;
        letter-spacing: 0.3px;
        white-space: nowrap;
      }

      .controls-divider {
        width: 1px;
        height: 12px;
        background: linear-gradient(
          to bottom,
          transparent,
          #cbd5e1,
          transparent
        );
      }

      .control-button {
        border-radius: 50% !important;
        height: 16px !important;
        width: 16px !important;
        font-size: 9px !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1) !important;
        min-width: 16px !important;
        padding: 0 !important;

        &.increase-button {
          color: #059669;
          background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
          border: 1px solid #a7f3d0;

          &:hover {
            background: linear-gradient(
              135deg,
              #10b981 0%,
              #059669 100%
            ) !important;
            color: #ffffff !important;
            transform: scale(1.15) !important;
          }
        }

        &.decrease-button {
          color: #dc2626;
          background: linear-gradient(135deg, #fef2f2 0%, #fecaca 100%);
          border: 1px solid #fca5a5;

          &:hover {
            background: linear-gradient(
              135deg,
              #ef4444 0%,
              #dc2626 100%
            ) !important;
            color: #ffffff !important;
            transform: scale(1.15) !important;
          }
        }
      }
    }
  }

  .batch-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    margin-bottom: 16px;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border: 1px solid #cbd5e1;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

    .batch-info {
      display: flex;
      align-items: center;
      gap: 16px;

      .batch-title {
        font-size: 14px;
        font-weight: 600;
        color: #1e293b;
        padding: 4px 12px;
        background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
        color: white;
        border-radius: 16px;
        font-size: 12px;
      }

      .selected-count {
        font-size: 13px;
        color: #64748b;
        background: #f1f5f9;
        padding: 4px 8px;
        border-radius: 4px;
        border: 1px solid #e2e8f0;
      }
    }

    .batch-controls {
      display: flex;
      align-items: center;
      gap: 12px;

      .adjust-label {
        font-size: 13px;
        color: #475569;
        font-weight: 500;
      }

      .ant-input-number {
        border-radius: 6px;
        border: 1px solid #d1d5db;

        &:focus,
        &:focus-within {
          border-color: #3b82f6;
          box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
        }
      }

      .ant-btn {
        border-radius: 6px;
        font-weight: 500;
        height: 32px;
        padding: 0 16px;

        &[type="primary"] {
          background: linear-gradient(135deg, #10b981 0%, #059669 100%);
          border: none;
          box-shadow: 0 2px 4px rgba(16, 185, 129, 0.2);
          color: #ffffff !important;

          &:hover {
            background: linear-gradient(135deg, #059669 0%, #047857 100%);
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(16, 185, 129, 0.3);
            color: #ffffff !important;
          }

          &:disabled {
            background: #9ca3af;
            transform: none;
            box-shadow: none;
          }
        }
      }
    }
  }

  &.batch-mode {
    .ant-table-tbody > tr {
      &.clickable-row {
        transition: all 0.2s ease;

        &:hover {
          background-color: rgba(24, 144, 255, 0.08) !important;
          transform: translateY(-1px);
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        &.selected-row {
          background-color: rgba(24, 144, 255, 0.15) !important;
          border-left: 3px solid #1890ff;
        }
      }

      &.disabled-row {
        opacity: 0.6;
        background-color: #f5f5f5 !important;

        &:hover {
          background-color: #f5f5f5 !important;
          transform: none;
          box-shadow: none;
        }
      }
    }
  }

  .cabin-report-cell {
    align-items: center;
    font-size: 14px;
    line-height: 1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 100%;

    .cabin-icon {
      flex-shrink: 0;
      margin-right: 1px;
      font-size: 12px;
      color: #1890ff;
    }

    .cabin-count {
      flex-shrink: 0;
      color: #1890ff;
      font-weight: 500;
      margin-right: 4px;
    }

    .cabin-separator {
      flex-shrink: 0;
      color: #d9d9d9;
      margin: 0 4px;
    }

    .cabin-item {
      display: inline-flex;
      align-items: center;
      margin-right: 2px;
      flex-shrink: 1;

      .cabin-date {
        color: #666;
        font-weight: 500;
        margin-right: 1px;
      }

      .cabin-tag {
        display: inline-block;
        padding: 0 2px;
        border-radius: 2px;
        font-size: 9px;
        line-height: 1.2;
        margin-left: 1px;
        vertical-align: middle;
        flex-shrink: 0;

        &.limit-tag {
          background-color: #f0f0f0;
          color: #666;
        }

        &.special-tag {
          background-color: #fff7e6;
          color: #fa8c16;
        }
      }

      .cabin-divider {
        color: #d9d9d9;
        margin: 0 2px;
        flex-shrink: 0;
      }
    }

    .cabin-more {
      font-size: 10px;
      color: #999;
      margin-left: 2px;
      flex-shrink: 0;
    }
  }

  .density-price-cell {
    display: flex;
    align-items: center;
    font-size: 13px;
    line-height: 1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 100%;

    .density-icon {
      flex-shrink: 0;
      margin-right: 2px;
      font-size: 12px;
      color: #52c41a;
    }

    .density-separator {
      flex-shrink: 0;
      color: #d9d9d9;
      margin: 0 4px;
    }

    .density-item {
      display: inline-flex;
      align-items: center;
      margin-right: 3px;
      flex-shrink: 1;

      .density-range-tag {
        display: inline-flex;
        flex-direction: column;
        align-items: center;
        background: linear-gradient(135deg, #f6ffed 0%, #d9f7be 100%);
        border: 1px solid #b7eb8f;
        border-radius: 6px;
        padding: 2px 6px;
        font-size: 10px;
        line-height: 1.2;
        transition: all 0.2s ease;

        &:hover {
          background: linear-gradient(135deg, #52c41a 0%, #389e0d 100%);
          color: white;
          transform: translateY(-1px);
          box-shadow: 0 2px 6px rgba(82, 196, 26, 0.3);
        }

        .range-text {
          font-weight: 600;
          color: #389e0d;
          font-size: 9px;
        }

        .avg-price {
          font-weight: 500;
          color: #52c41a;
          font-size: 8px;
          margin-top: 1px;
        }

        &:hover .range-text,
        &:hover .avg-price {
          color: white;
        }
      }

      .density-divider {
        color: #d9d9d9;
        margin: 0 2px;
        flex-shrink: 0;
      }
    }

    .density-more {
      font-size: 10px;
      color: #999;
      margin-left: 2px;
      flex-shrink: 0;
      background: #f5f5f5;
      border-radius: 4px;
      padding: 1px 4px;
      border: 1px solid #d9d9d9;
    }
  }

  .expanded-table-container {
    background: #fafafa;
    border-radius: 6px;
    margin: 8px 0;

    .expanded-table-header {
      display: flex;
      justify-content: flex-start;
      align-items: center;
      margin-bottom: 12px;
      padding-bottom: 8px;
      border-bottom: 1px solid #e8e8e8;

      .expanded-table-title {
        font-size: 14px;
        font-weight: 600;
        color: #1f2937;
        position: relative;
        padding-left: 12px;
        margin-right: 20px;

        &:before {
          content: "";
          position: absolute;
          left: 0;
          top: 50%;
          transform: translateY(-50%);
          width: 3px;
          height: 14px;
          background-color: #1797e1;
          border-radius: 2px;
        }
      }
    }

    .expanded-inner-table {
      .ant-table {
        background: #ffffff;
        border-radius: 4px;
        border: 1px solid #e8e8e8;

        .ant-table-thead > tr > th {
          background-color: #f8f9fa;
          color: #1f2937;
          font-weight: 500;
          border-bottom: 1px solid #e8e8e8;
          padding: 8px 12px;
          font-size: 13px;
        }

        .ant-table-tbody > tr > td {
          padding: 8px 12px;
          border-bottom: 1px solid #f0f0f0;
          font-size: 13px;
        }

        .ant-table-tbody > tr:hover > td {
          background-color: #f5f7fa;
        }

        .ant-table-tbody > tr:last-child > td {
          border-bottom: none;
        }
      }

      .editable-cell-value-wrap {
        cursor: pointer;
        border-radius: 4px;
        display: flex;
        align-items: center;
        justify-content: center;
        min-height: 24px;
        padding: 2px 8px;

        &:hover {
          border: 1px solid #d9d9d9;
          background-color: #f5f5f5;
        }
      }

      .price-cell-with-horizontal-controls {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: 4px;

        .price-value-area {
          cursor: pointer;
          border-radius: 4px;
          display: flex;
          align-items: center;
          justify-content: center;
          min-height: 24px;
          padding: 2px 8px;

          &:hover {
            border: 1px solid #d9d9d9;
            background-color: #f5f5f5;
          }
        }

        .horizontal-controls-group {
          display: flex;
          align-items: center;
          gap: 2px;
          padding: 1px 3px;
          background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
          border: 1px solid #e2e8f0;
          border-radius: 12px;
          box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);

          .control-button {
            border-radius: 50% !important;
            height: 14px !important;
            width: 14px !important;
            font-size: 8px !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            min-width: 14px !important;
            padding: 0 !important;
          }
        }
      }

      .operation-buttons {
        .ant-btn {
          border-radius: 4px;
          height: 24px;
          width: 24px;
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 0;

          &.view-button {
            color: #10b981;
            &:hover {
              background-color: #ecfdf5;
            }
          }

          &.edit-button {
            color: @primary-color;
            &:hover {
              background-color: #e6f7ff;
            }
          }

          &.delete-button {
            color: #ef4444;
            &:hover {
              background-color: #fef2f2;
            }
          }

          &.save-button {
            color: #22c55e;
            &:hover {
              background-color: #dcfce7;
            }
          }

          &.cancel-button {
            color: #f97316;
            &:hover {
              background-color: #fff7ed;
            }
          }
        }
      }
    }
  }

  .ant-table-expanded-row > td {
    padding: 0 !important;
    background-color: #fafafa !important;
  }

  .ant-table-expand-icon-col {
    width: 40px;
    min-width: 40px;
  }
}

.cabin-report-tooltip {
  .tooltip-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 14px 20px;
    background: linear-gradient(135deg, @primary-color 0%, #0f7bb8 100%);
    border-radius: 11px 11px 0 0;
    margin: 0;

    .tooltip-title {
      color: #ffffff;
      font-weight: 600;
      font-size: 14px;
      margin: 0;
    }

    .tooltip-count {
      color: #e0e7ff;
      font-size: 12px;
      background: rgba(255, 255, 255, 0.2);
      padding: 2px 8px;
      border-radius: 12px;
      font-weight: 500;
    }
  }

  .tooltip-content {
    padding: 12px;
    max-height: 80vh;
    overflow-y: auto;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;

    &:has(.cabin-item-detail:only-child) {
      grid-template-columns: 1fr;
      max-width: 400px;
    }

    &::-webkit-scrollbar {
      width: 4px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f5f9;
      border-radius: 2px;
    }

    &::-webkit-scrollbar-thumb {
      background: #cbd5e1;
      border-radius: 2px;

      &:hover {
        background: #94a3b8;
      }
    }

    .cabin-item-detail {
      background: #ffffff;
      border: 1px solid #e8f4fd;
      border-radius: 8px;
      padding: 12px;
      transition: all 0.2s ease;
      box-shadow: 0 1px 3px rgba(23, 151, 225, 0.08);

      &:hover {
        border-color: @primary-color;
        box-shadow: 0 3px 12px rgba(23, 151, 225, 0.15);
        transform: translateY(-1px);
      }

      .cabin-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;
        gap: 12px;

        .cabin-index {
          background: linear-gradient(135deg, @primary-color 0%, #0f7bb8 100%);
          color: #ffffff;
          font-size: 11px;
          font-weight: 600;
          padding: 3px 8px;
          border-radius: 12px;
          min-width: 28px;
          text-align: center;
        }

        .cabin-date {
          color: #1e293b;
          font-weight: 600;
          font-size: 13px;
        }

        .cabin-badges {
          display: flex;
          gap: 4px;

          .badge {
            font-size: 10px;
            font-weight: 500;
            padding: 1px 6px;
            border-radius: 8px;
            line-height: 1.4;

            &.limit-badge {
              background: linear-gradient(135deg, #fff7ed 0%, #fed7aa 100%);
              color: #c2410c;
              border: 1px solid #ea580c;
            }

            &.special-badge {
              background: linear-gradient(135deg, #fef2f2 0%, #fecaca 100%);
              color: #dc2626;
              border: 1px solid #ef4444;
            }

            &.parts-badge {
              background: linear-gradient(135deg, #f0f9ff 0%, #dbeafe 100%);
              color: #1d4ed8;
              border: 1px solid @primary-color;
            }

            &.inquiry-badge {
              background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
              color: #475569;
              border: 1px solid #64748b;
            }
          }
        }
      }

      .cabin-details {
        .detail-row {
          display: flex;
          align-items: center;
          margin-bottom: 4px;
          font-size: 12px;

          &:last-child {
            margin-bottom: 0;
          }

          .detail-label {
            color: #64748b;
            font-weight: 500;
            min-width: 70px;
            margin-right: 8px;
          }

          .detail-value {
            color: #1e293b;
            font-weight: 400;
            flex: 1;

            .price-change-item {
              background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
              border: 1px solid @primary-color;
              border-radius: 6px;
              padding: 6px 10px;
              margin: 3px 0;
              font-size: 11px;
              color: #0f7bb8;
              font-weight: 500;
              box-shadow: 0 1px 3px rgba(23, 151, 225, 0.1);
            }

            .special-price-item {
              background: linear-gradient(135deg, #fff7ed 0%, #fed7aa 100%);
              border: 1px solid #ea580c;
              border-radius: 6px;
              padding: 6px 10px;
              margin: 3px 0;
              font-size: 11px;
              color: #c2410c;
              font-weight: 500;
              box-shadow: 0 1px 3px rgba(234, 88, 12, 0.1);
            }
          }
        }
      }
    }
  }
}

.ant-tooltip:has(.cabin-report-tooltip) {
  .ant-tooltip-inner {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border: 1px solid #e8f4fd;
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(23, 151, 225, 0.15);
    padding: 0;
  }
}

.custom-tooltip .ant-tooltip-arrow::before {
  background: @primary-color;
}

.density-price-tooltip {
  .tooltip-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background: linear-gradient(135deg, #52c41a 0%, #389e0d 100%);
    border-radius: 8px 8px 0 0;
    margin: 0;

    .tooltip-title {
      color: #ffffff;
      font-weight: 600;
      font-size: 13px;
      margin: 0;
    }

    .tooltip-count {
      color: #f6ffed;
      font-size: 11px;
      background: rgba(255, 255, 255, 0.2);
      padding: 2px 6px;
      border-radius: 10px;
      font-weight: 500;
    }
  }

  .tooltip-content {
    padding: 10px;
    max-height: 60vh;
    overflow-y: auto;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 8px;

    &:has(.density-item-detail:only-child) {
      grid-template-columns: 1fr;
      max-width: 300px;
    }

    &::-webkit-scrollbar {
      width: 4px;
    }

    &::-webkit-scrollbar-track {
      background: #f6ffed;
      border-radius: 2px;
    }

    &::-webkit-scrollbar-thumb {
      background: #b7eb8f;
      border-radius: 2px;

      &:hover {
        background: #95de64;
      }
    }

    .density-item-detail {
      background: #ffffff;
      border: 1px solid #d9f7be;
      border-radius: 6px;
      padding: 10px;
      transition: all 0.2s ease;
      box-shadow: 0 1px 3px rgba(82, 196, 26, 0.08);

      &:hover {
        border-color: #52c41a;
        box-shadow: 0 3px 10px rgba(82, 196, 26, 0.15);
        transform: translateY(-1px);
      }

      .density-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;
        gap: 8px;

        .density-index {
          background: linear-gradient(135deg, #52c41a 0%, #389e0d 100%);
          color: #ffffff;
          font-size: 10px;
          font-weight: 600;
          padding: 2px 6px;
          border-radius: 10px;
          min-width: 24px;
          text-align: center;
        }

        .density-range {
          color: #1e293b;
          font-weight: 600;
          font-size: 12px;
          flex: 1;
        }
      }

      .density-prices {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 4px;

        .price-row {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 3px 6px;
          background: linear-gradient(135deg, #f6ffed 0%, #d9f7be 100%);
          border-radius: 4px;
          font-size: 11px;

          .price-label {
            color: #389e0d;
            font-weight: 600;
          }

          .price-value {
            color: #1e293b;
            font-weight: 500;
          }
        }
      }
    }
  }
}

.ant-tooltip:has(.density-price-tooltip) {
  .ant-tooltip-inner {
    background: linear-gradient(135deg, #ffffff 0%, #f6ffed 100%);
    border: 1px solid #d9f7be;
    border-radius: 8px;
    box-shadow: 0 6px 24px rgba(82, 196, 26, 0.15);
    padding: 0;
  }
}
